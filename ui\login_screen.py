"""
شاشة الدخول المتقدمة والكبيرة للبرنامج
"""

import sys
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                             QLineEdit, QPushButton, QFrame, QApplication,
                             QGraphicsDropShadowEffect, QCheckBox, QSpacerItem,
                             QSizePolicy, QGridLayout, QMessageBox)
from PyQt5.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve, pyqtSignal, QRect
from PyQt5.QtGui import QFont, QPixmap, QPainter, QBrush, QColor, QPen, QLinearGradient
from database import get_session, User
import hashlib


class LoginScreen(QWidget):
    """شاشة الدخول المتقدمة والكبيرة"""
    
    # إشارة لإرسال بيانات المستخدم عند نجاح تسجيل الدخول
    login_successful = pyqtSignal(object, object)  # session, user
    
    def __init__(self):
        super().__init__()
        self.session = None
        self.setup_ui()
        self.setup_animations()
        
    def setup_ui(self):
        """إعداد واجهة شاشة الدخول المتقدمة"""
        # إعداد النافذة الأساسية - تصميم محسن
        self.setWindowTitle("🔐 Smart Finish - نظام تسجيل الدخول")
        self.setFixedSize(550, 700)  # أبعاد محسنة
        self.setWindowFlags(Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        
        # إنشاء التخطيط الرئيسي مع تصميم محسن
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(25, 25, 25, 25)
        main_layout.setSpacing(20)

        # تطبيق الخلفية المتدرجة الأساسية للبرنامج
        self.setStyleSheet("""
            LoginScreen {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #64748B, stop:0.5 #475569,
                    stop:0.6 #334155, stop:0.7 #1E293B, stop:0.8 #0F172A,
                    stop:0.9 #1E293B, stop:1 #334155);
                border-radius: 25px;
                border: 2px solid rgba(255, 255, 255, 0.1);
            }
        """)
        
        # إنشاء منطقة العنوان
        self.create_header_section(main_layout)

        # إنشاء منطقة تسجيل الدخول
        self.create_login_section(main_layout)

        # إنشاء منطقة الأزرار
        self.create_buttons_section(main_layout)

        # إنشاء منطقة التذييل
        self.create_footer_section(main_layout)
        
    def create_header_section(self, parent_layout):
        """إنشاء منطقة العنوان المحسنة"""
        header_frame = QFrame()
        header_frame.setFixedHeight(180)
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.05),
                    stop:0.5 rgba(255, 255, 255, 0.1),
                    stop:1 rgba(255, 255, 255, 0.05));
                border-radius: 20px;
                border: 1px solid rgba(255, 255, 255, 0.15);
                margin: 5px;
            }
        """)
        header_layout = QVBoxLayout(header_frame)
        header_layout.setAlignment(Qt.AlignCenter)
        header_layout.setSpacing(12)
        
        # شعار الشركة
        logo_label = QLabel("💼")
        logo_label.setAlignment(Qt.AlignCenter)
        logo_label.setStyleSheet("""
            QLabel {
                font-size: 64px;
                color: #60A5FA;
                background: qradial-gradient(circle,
                    rgba(96, 165, 250, 0.2) 0%,
                    rgba(59, 130, 246, 0.15) 50%,
                    rgba(37, 99, 235, 0.1) 100%);
                border-radius: 40px;
                padding: 15px;
                border: 2px solid rgba(96, 165, 250, 0.3);
                min-width: 80px;
                min-height: 80px;
            }
        """)

        # العنوان الرئيسي - محسن
        title_label = QLabel("شركة Smart Finish")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 32px;
                font-weight: bold;
                font-family: 'Segoe UI', 'Arial', 'Tahoma', sans-serif;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
                padding: 8px;
                margin: 5px;
            }
        """)
        
        # العنوان الفرعي - محسن
        subtitle_label = QLabel("نظام المحاسبة الإداري المتطور")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.85);
                font-size: 18px;
                font-weight: 500;
                font-family: 'Segoe UI', 'Arial', 'Tahoma', sans-serif;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
                padding: 5px;
                margin: 2px;
            }
        """)

        # إضافة العناصر للتخطيط
        header_layout.addWidget(logo_label)
        header_layout.addWidget(title_label)
        header_layout.addWidget(subtitle_label)
        
        parent_layout.addWidget(header_frame)
        
    def create_login_section(self, parent_layout):
        """إنشاء منطقة تسجيل الدخول المحسنة"""
        login_frame = QFrame()
        login_frame.setFixedHeight(280)
        login_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.08),
                    stop:0.3 rgba(96, 165, 250, 0.12),
                    stop:0.7 rgba(59, 130, 246, 0.1),
                    stop:1 rgba(255, 255, 255, 0.08));
                border-radius: 18px;
                border: 2px solid rgba(96, 165, 250, 0.25);
                margin: 5px;
            }
        """)
        
        # إضافة تأثير الظل لإطار تسجيل الدخول
        login_shadow = QGraphicsDropShadowEffect()
        login_shadow.setBlurRadius(20)
        login_shadow.setColor(QColor(0, 0, 0, 50))
        login_shadow.setOffset(0, 5)
        login_frame.setGraphicsEffect(login_shadow)
        
        login_layout = QGridLayout(login_frame)
        login_layout.setContentsMargins(30, 25, 30, 25)  # مسافات أكبر
        login_layout.setSpacing(20)  # مسافة أكبر
        
        # تسمية اسم المستخدم
        username_label = QLabel("👤 اسم المستخدم:")
        username_label.setStyleSheet(self.get_label_style())
        
        # حقل اسم المستخدم - ارتفاع أكبر
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("أدخل اسم المستخدم...")
        self.username_input.setText("admin")  # قيمة افتراضية
        self.username_input.setStyleSheet(self.get_input_style())
        self.username_input.setFixedHeight(50)  # ارتفاع أكبر
        
        # تسمية كلمة المرور
        password_label = QLabel("🔒 كلمة المرور:")
        password_label.setStyleSheet(self.get_label_style())
        
        # حقل كلمة المرور - ارتفاع أكبر
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("أدخل كلمة المرور...")
        self.password_input.setText("admin")  # قيمة افتراضية
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setStyleSheet(self.get_input_style())
        self.password_input.setFixedHeight(50)  # ارتفاع أكبر
        
        # خانة اختيار "تذكرني" - محسنة
        self.remember_checkbox = QCheckBox("🔄 تذكر بيانات الدخول")
        self.remember_checkbox.setChecked(True)
        self.remember_checkbox.setStyleSheet("""
            QCheckBox {
                color: #FFFFFF;
                font-size: 16px;
                font-weight: 600;
                font-family: 'Segoe UI', 'Arial', 'Tahoma', sans-serif;
                spacing: 12px;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
                padding: 8px;
            }
            QCheckBox::indicator {
                width: 22px;
                height: 22px;
                border-radius: 6px;
                border: 2px solid rgba(96, 165, 250, 0.6);
                background: rgba(255, 255, 255, 0.15);
            }
            QCheckBox::indicator:checked {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #60A5FA, stop:1 #3B82F6);
                border: 2px solid #60A5FA;
            }
            QCheckBox::indicator:hover {
                border: 2px solid #60A5FA;
                background: rgba(96, 165, 250, 0.2);
            }
        """)
        
        # ترتيب العناصر في الشبكة
        login_layout.addWidget(username_label, 0, 0)
        login_layout.addWidget(self.username_input, 0, 1)
        login_layout.addWidget(password_label, 1, 0)
        login_layout.addWidget(self.password_input, 1, 1)
        login_layout.addWidget(self.remember_checkbox, 2, 0, 1, 2, Qt.AlignCenter)
        
        parent_layout.addWidget(login_frame)
        
    def create_buttons_section(self, parent_layout):
        """إنشاء منطقة الأزرار المحسنة"""
        buttons_frame = QFrame()
        buttons_frame.setFixedHeight(140)
        buttons_frame.setStyleSheet("""
            QFrame {
                background: transparent;
                margin: 10px;
            }
        """)
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setContentsMargins(25, 25, 25, 25)
        buttons_layout.setSpacing(25)
        
        # زر تسجيل الدخول - محسن
        self.login_button = QPushButton("🚀 تسجيل الدخول")
        self.login_button.setFixedSize(220, 65)
        self.style_advanced_button(self.login_button, 'emerald')
        self.login_button.clicked.connect(self.handle_login)

        # زر الإلغاء/الخروج - محسن
        self.cancel_button = QPushButton("❌ إلغاء")
        self.cancel_button.setFixedSize(220, 65)
        self.style_advanced_button(self.cancel_button, 'danger')
        self.cancel_button.clicked.connect(self.close)
        
        # إضافة مساحة مرنة
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.cancel_button)
        buttons_layout.addWidget(self.login_button)
        buttons_layout.addStretch()
        
        parent_layout.addWidget(buttons_frame)

    def create_footer_section(self, parent_layout):
        """إنشاء منطقة التذييل المحسنة"""
        footer_frame = QFrame()
        footer_frame.setFixedHeight(60)
        footer_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.03),
                    stop:0.5 rgba(96, 165, 250, 0.08),
                    stop:1 rgba(255, 255, 255, 0.03));
                border-radius: 15px;
                border: 1px solid rgba(255, 255, 255, 0.1);
                margin: 5px;
            }
        """)
        footer_layout = QVBoxLayout(footer_frame)
        footer_layout.setAlignment(Qt.AlignCenter)
        footer_layout.setSpacing(0)

        # معلومات حقوق الطبع - محسنة
        copyright_label = QLabel("© 2024 Smart Finish - جميع الحقوق محفوظة")
        copyright_label.setAlignment(Qt.AlignCenter)
        copyright_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.8);
                font-size: 14px;
                font-weight: 500;
                font-family: 'Segoe UI', 'Arial', 'Tahoma', sans-serif;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
                padding: 8px;
            }
        """)

        footer_layout.addWidget(copyright_label)

        parent_layout.addWidget(footer_frame)

    def get_label_style(self):
        """الحصول على نمط التسميات"""
        return """
            QLabel {
                color: white;
                font-size: 18px;
                font-weight: bold;
                font-family: 'Arial', 'Tahoma', sans-serif;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
                padding: 5px;
            }
        """

    def get_input_style(self):
        """الحصول على نمط حقول الإدخال المحسن"""
        return """
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.5 rgba(248, 250, 252, 0.98),
                    stop:1 rgba(241, 245, 249, 0.95));
                border: 3px solid rgba(96, 165, 250, 0.4);
                border-radius: 15px;
                padding: 15px 18px;
                font-size: 16px;
                font-weight: 600;
                font-family: 'Segoe UI', 'Arial', 'Tahoma', sans-serif;
                color: #1E293B;
                selection-background-color: #60A5FA;
            }
            QLineEdit:focus {
                border: 3px solid #60A5FA;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 1.0),
                    stop:0.5 rgba(248, 250, 252, 1.0),
                    stop:1 rgba(255, 255, 255, 1.0));
                box-shadow: 0 0 15px rgba(96, 165, 250, 0.4);
            }
            QLineEdit::placeholder {
                color: rgba(30, 41, 59, 0.5);
                font-style: italic;
                font-weight: 400;
            }
        """

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور للأزرار مع ألوان البرنامج الأساسية"""
        try:
            # تحديد الألوان المتماشية مع البرنامج
            colors = {
                'emerald': {
                    'bg_start': '#0F766E', 'bg_mid': '#14B8A6', 'bg_end': '#0D9488', 'bg_bottom': '#06B6D4',
                    'hover_start': '#14B8A6', 'hover_mid': '#2DD4BF', 'hover_end': '#5EEAD4', 'hover_bottom': '#99F6E4',
                    'hover_border': '#14B8A6', 'pressed_start': '#134E4A', 'pressed_mid': '#0F766E',
                    'pressed_end': '#0D9488', 'pressed_bottom': '#0891B2', 'pressed_border': '#0F766E',
                    'border': '#14B8A6', 'text': '#ffffff', 'shadow': 'rgba(20, 184, 166, 0.5)'
                },
                'danger': {
                    'bg_start': '#B91C1C', 'bg_mid': '#DC2626', 'bg_end': '#EF4444', 'bg_bottom': '#F87171',
                    'hover_start': '#DC2626', 'hover_mid': '#EF4444', 'hover_end': '#F87171', 'hover_bottom': '#FCA5A5',
                    'hover_border': '#EF4444', 'pressed_start': '#991B1B', 'pressed_mid': '#B91C1C',
                    'pressed_end': '#DC2626', 'pressed_bottom': '#EF4444', 'pressed_border': '#B91C1C',
                    'border': '#DC2626', 'text': '#ffffff', 'shadow': 'rgba(220, 38, 38, 0.5)'
                }
            }

            # الحصول على ألوان الزر المحدد
            color_scheme = colors.get(button_type, colors['emerald'])

            # تحديد نمط القائمة المنسدلة إذا كان الزر يحتوي على قائمة
            menu_indicator = "::menu-indicator { width: 0px; }" if not has_menu else ""

            # تطبيق التصميم المحسن مع ألوان البرنامج
            style = f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['bg_start']},
                        stop:0.3 {color_scheme['bg_mid']},
                        stop:0.7 {color_scheme['bg_end']},
                        stop:1 {color_scheme['bg_bottom']});
                    color: {color_scheme['text']};
                    border: 3px solid {color_scheme['border']};
                    border-radius: 18px;
                    padding: 12px 20px;
                    font-weight: 700;
                    font-size: 16px;
                    font-family: 'Segoe UI', 'Arial', 'Tahoma', sans-serif;
                    text-align: center;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.6);
                    box-shadow: 0 8px 20px {color_scheme['shadow']},
                               inset 0 2px 0 rgba(255, 255, 255, 0.2),
                               inset 0 -2px 0 rgba(0, 0, 0, 0.2);
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['hover_start']},
                        stop:0.3 {color_scheme['hover_mid']},
                        stop:0.7 {color_scheme['hover_end']},
                        stop:1 {color_scheme['hover_bottom']});
                    border: 3px solid {color_scheme['hover_border']};
                    transform: translateY(-2px) scale(1.05);
                    box-shadow: 0 12px 30px {color_scheme['shadow']},
                               inset 0 3px 0 rgba(255, 255, 255, 0.3),
                               inset 0 -3px 0 rgba(0, 0, 0, 0.3);
                    text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.8);
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['pressed_start']},
                        stop:0.3 {color_scheme['pressed_mid']},
                        stop:0.7 {color_scheme['pressed_end']},
                        stop:1 {color_scheme['pressed_bottom']});
                    border: 3px solid {color_scheme['pressed_border']};
                    transform: translateY(1px) scale(0.95);
                    box-shadow: inset 0 4px 8px rgba(0, 0, 0, 0.5),
                               0 4px 8px {color_scheme['shadow']};
                    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.9);
                }}
                QPushButton:disabled {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #64748B, stop:0.5 #475569, stop:1 #334155);
                    color: #94A3B8;
                    border: 3px solid #64748B;
                    box-shadow: none;
                    text-shadow: none;
                }}
                {menu_indicator}
            """

            button.setStyleSheet(style)

        except Exception as e:
            print(f"خطأ في تطبيق تصميم الزر: {str(e)}")

    def setup_animations(self):
        """إعداد الرسوم المتحركة المتقدمة"""
        # رسوم متحركة لظهور النافذة
        self.fade_animation = QPropertyAnimation(self, b"windowOpacity")
        self.fade_animation.setDuration(1000)
        self.fade_animation.setStartValue(0.0)
        self.fade_animation.setEndValue(1.0)
        self.fade_animation.setEasingCurve(QEasingCurve.OutCubic)

        # رسوم متحركة لحركة النافذة
        self.move_animation = QPropertyAnimation(self, b"geometry")
        self.move_animation.setDuration(800)
        self.move_animation.setEasingCurve(QEasingCurve.OutBack)

        # بدء الرسوم المتحركة عند الظهور
        QTimer.singleShot(100, self.start_entrance_animation)

    def start_entrance_animation(self):
        """بدء رسوم متحركة للدخول"""
        # تحديد الموضع النهائي (وسط الشاشة)
        screen = QApplication.desktop().screenGeometry()
        x = (screen.width() - self.width()) // 2
        y = (screen.height() - self.height()) // 2

        # تحديد الموضع الابتدائي (أعلى الشاشة)
        start_rect = QRect(x, -self.height(), self.width(), self.height())
        end_rect = QRect(x, y, self.width(), self.height())

        # إعداد الرسوم المتحركة
        self.setGeometry(start_rect)
        self.move_animation.setStartValue(start_rect)
        self.move_animation.setEndValue(end_rect)

        # بدء الرسوم المتحركة
        self.fade_animation.start()
        self.move_animation.start()

    def handle_login(self):
        """معالجة عملية تسجيل الدخول"""
        username = self.username_input.text().strip()
        password = self.password_input.text().strip()

        # التحقق من صحة البيانات
        if not username or not password:
            self.show_error_message("⚠️ خطأ في البيانات",
                                   "يرجى إدخال اسم المستخدم وكلمة المرور")
            return

        try:
            # إنشاء جلسة قاعدة البيانات
            self.session = get_session()

            # البحث عن المستخدم
            user = self.session.query(User).filter_by(username=username).first()

            if user and user.password == password:
                # نجح تسجيل الدخول
                self.show_success_message("✅ نجح تسجيل الدخول",
                                        f"مرحباً بك {user.full_name}")

                # إرسال إشارة نجاح تسجيل الدخول
                QTimer.singleShot(1500, lambda: self.login_successful.emit(self.session, user))
                QTimer.singleShot(1500, self.close)

            else:
                # فشل تسجيل الدخول
                self.show_error_message("❌ خطأ في تسجيل الدخول",
                                       "اسم المستخدم أو كلمة المرور غير صحيحة")

        except Exception as e:
            self.show_error_message("❌ خطأ في النظام",
                                   f"حدث خطأ أثناء تسجيل الدخول:\n{str(e)}")

    def show_success_message(self, title, message):
        """عرض رسالة نجاح متقدمة"""
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setIcon(QMessageBox.Information)
        msg_box.setWindowFlags(Qt.Dialog | Qt.CustomizeWindowHint | Qt.WindowTitleHint)

        # تطبيق نمط متقدم لرسالة النجاح
        msg_box.setStyleSheet("""
            QMessageBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #10B981, stop:0.5 #059669, stop:1 #047857);
                border-radius: 15px;
                border: 2px solid #34D399;
            }
            QMessageBox QLabel {
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 10px;
            }
            QMessageBox QPushButton {
                background: rgba(255, 255, 255, 0.9);
                border: 2px solid #10B981;
                border-radius: 8px;
                color: #047857;
                font-weight: bold;
                padding: 8px 16px;
                min-width: 80px;
            }
            QMessageBox QPushButton:hover {
                background: white;
                border: 2px solid #34D399;
            }
        """)

        msg_box.exec_()

    def show_error_message(self, title, message):
        """عرض رسالة خطأ متقدمة"""
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setIcon(QMessageBox.Critical)
        msg_box.setWindowFlags(Qt.Dialog | Qt.CustomizeWindowHint | Qt.WindowTitleHint)

        # تطبيق نمط متقدم لرسالة الخطأ
        msg_box.setStyleSheet("""
            QMessageBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #DC2626, stop:0.5 #B91C1C, stop:1 #991B1B);
                border-radius: 15px;
                border: 2px solid #F87171;
            }
            QMessageBox QLabel {
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 10px;
            }
            QMessageBox QPushButton {
                background: rgba(255, 255, 255, 0.9);
                border: 2px solid #DC2626;
                border-radius: 8px;
                color: #991B1B;
                font-weight: bold;
                padding: 8px 16px;
                min-width: 80px;
            }
            QMessageBox QPushButton:hover {
                background: white;
                border: 2px solid #F87171;
            }
        """)

        msg_box.exec_()

    def keyPressEvent(self, event):
        """معالجة ضغط المفاتيح"""
        if event.key() == Qt.Key_Return or event.key() == Qt.Key_Enter:
            self.handle_login()
        elif event.key() == Qt.Key_Escape:
            self.close()
        else:
            super().keyPressEvent(event)

    def closeEvent(self, event):
        """معالجة إغلاق النافذة"""
        if self.session:
            self.session.close()
        event.accept()


# اختبار شاشة الدخول
if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    # إعداد قاعدة البيانات للاختبار
    from database import init_db
    init_db()

    login_screen = LoginScreen()
    login_screen.show()

    sys.exit(app.exec_())
